#ifndef JOINT_UTILS_H
#define JOINT_UTILS_H

#include <pinocchio/multibody/model.hpp>
#include <vector>
#include <string>
#include <set>

namespace robot_controller {

/**
 * @brief 关节类型枚举
 */
enum class JointType {
    ACTIVE,  // 主动关节
    MIMIC    // 从动关节（mimic joint）
};

/**
 * @brief 关节信息结构体
 */
struct JointInfo {
    pinocchio::JointIndex index;
    std::string name;
    JointType type;
    int nq;  // 位置自由度
    int nv;  // 速度自由度
    
    JointInfo(pinocchio::JointIndex idx, const std::string& n, JointType t, int q, int v)
        : index(idx), name(n), type(t), nq(q), nv(v) {}
};

/**
 * @brief 获取机器人模型中的从动关节列表
 * @return 从动关节名称集合
 */
std::set<std::string> getMimicJoints();

/**
 * @brief 分析关节类型
 * @param model Pinocchio模型
 * @param joint_name 关节名称
 * @return 关节类型
 */
JointType analyzeJointType(const pinocchio::Model& model, const std::string& joint_name);

/**
 * @brief 获取指定前缀的主动关节
 * @param model Pinocchio模型
 * @param prefix 关节名称前缀（如"r_joint"表示右臂关节）
 * @return 主动关节索引列表
 */
std::vector<pinocchio::JointIndex> getActiveJoints(const pinocchio::Model& model, const std::string& prefix);

/**
 * @brief 获取指定前缀的从动关节
 * @param model Pinocchio模型
 * @param prefix 关节名称前缀
 * @return 从动关节索引列表
 */
std::vector<pinocchio::JointIndex> getMimicJoints(const pinocchio::Model& model, const std::string& prefix);

/**
 * @brief 获取关节详细信息
 * @param model Pinocchio模型
 * @param prefix 关节名称前缀
 * @return 关节信息列表
 */
std::vector<JointInfo> getJointInfo(const pinocchio::Model& model, const std::string& prefix);

/**
 * @brief 计算主动关节的自由度
 * @param model Pinocchio模型
 * @param prefix 关节名称前缀
 * @return 主动关节的总自由度
 */
int getActiveDOF(const pinocchio::Model& model, const std::string& prefix);

} // namespace robot_controller

#endif // JOINT_UTILS_H
