<?xml version="1.0" encoding="utf-8"?>

<robot name="S1_robot">
    <link name="world"/>

    <!-- <link name="base_0">
        <inertial>
            <origin xyz="0.00063163 0.0062432 -0.64577" rpy="0 0 0" />
            <mass value="6.8529" />
            <inertia ixx="0.052998" ixy="1.7166E-07" ixz="8.6825E-08" iyy="0.039832" iyz="-0.0021557" izz="0.045404" />
        </inertial>
        <visual>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
              <mesh filename="package://robot_controller/description/meshes/base_0.STL" />
          </geometry>
          <material name="">
              <color rgba="0.89804 0.91765 0.92941 1" />
          </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
              <mesh filename="package://robot_controller/description/meshes/base_0.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="world2base" type="fixed">
        <parent link="world" />
        <child link="base_0"/>
        <origin xyz="0 0 1.225" rpy="0 0 0"/>
    </joint> -->

    <link name="base_1">
        <inertial>
            <origin xyz="0.00497628598766181 -0.270000000000005 -0.0806036893535225" rpy="0 0 0" />
            <mass value="1.67606089942984" />
            <inertia ixx="0.0119402044375324" ixy="1.27158113564894E-17" ixz="-0.000852740591259341" iyy="0.0145086440338916" iyz="-9.47423299578457E-17" izz="0.0166223996856065" />
        </inertial>
        <visual>
            <origin xyz="-0.27 0 -0.01" rpy="0 0 1.5708" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="world2base" type="fixed">
        <parent link="world" />
        <child link="base_1"/>
        <origin xyz="0 0 0.145" rpy="0 0 0"/>
    </joint>

    <link name="base_2">
        <inertial>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <mass value="2.57822769233319" />
            <inertia ixx="0.0254765308482837" ixy="1.05377567934407E-11" ixz="-5.29492003108919E-11" iyy="0.0174624849009064" iyz="0.0010776697151391" izz="0.020159156313896" />
        </inertial>
        <visual>
            <origin xyz="0 -1.022 -0.295" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="base_joint1" type="revolute">
        <origin xyz="0 0 0" rpy="-1.5708 0 0" />
        <parent link="base_1" />
        <child link="base_2" />
        <axis xyz="0 0 1" />
        <limit lower="-1.57" upper="1.57" effort="1000" velocity="10" />
    </joint>

    <link name="base_3">
        <inertial>
            <origin xyz="0.00166571211462507 0.145515714811915 0.297080995533155" rpy="0 0 0" />
            <mass value="2.59859764976372" />
            <inertia ixx="0.0130128079963049" ixy="-8.68358902434353E-08" ixz="1.71715038905579E-07" iyy="0.0113194223833036" iyz="0.000225300881864542" izz="0.00773255955908023" />
        </inertial>
        <visual>
            <origin xyz="0 -0.422 -0.295" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/base_0_3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="base_joint2" type="revolute">
        <origin xyz="0 -0.6 0" rpy="0 0 0" />
        <parent link="base_2" />
        <child link="base_3" />
        <axis xyz="0 0 1" />
        <limit lower="-1.57" upper="1.57" effort="1000" velocity="10" />
    </joint>

    <link name="base_arm" />

    <joint name="base_joint3" type="fixed">
        <parent link="base_3" />
        <child link="base_arm" />
        <origin xyz="0 -0.48 0" rpy="1.5708 0 0" />
    </joint>

    <link name="l_Link1">
        <inertial>
            <origin xyz="6.31445167356137E-05 -0.000887062704731684 -3.15110131199337E-05" rpy="0 0 0" />
            <mass value="0.436403784733489" />
            <inertia ixx="0.000304981735988441" ixy="-2.65454403808391E-12" ixz="-8.56215025516675E-08" iyy="0.000298813758622503" iyz="-3.19657194657026E-07" izz="0.000446290979573795" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint1" type="revolute">
        <origin xyz="0 0.16505 0" rpy="3.14 0 0" />
        <parent link="base_arm" />
        <child link="l_Link1" />
        <axis xyz="0 0 1" />
        <limit lower="-2.02" upper="2.02" effort="100" velocity="1" />
    </joint>

    <link name="l_Link2">
        <inertial>
            <origin xyz="-0.00548731670358221 3.68320164048086E-05 0.101178720910149" rpy="0 0 0" />
            <mass value="0.585126701861528" />
            <inertia ixx="0.000653609977994595" ixy="-5.94192878114861E-07" ixz="1.87408981422656E-05" iyy="0.000552218961920917" iyz="-6.06730800625581E-07" izz="0.00054746322567633" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="3.14 0 3.14" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint2" type="revolute">
        <origin xyz="0 0 0" rpy="-1.57 0 0" />
        <parent link="l_Link1" />
        <child link="l_Link2" />
        <axis xyz="0 0 1" />
        <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="l_Link3">
        <inertial>
            <origin xyz="-0.208209353747979 -0.000661214294713497 0.000149105610438405" rpy="0 0 0" />
            <mass value="0.505058956879044" />
            <inertia ixx="0.000216923846588862" ixy="-2.10730757023179E-05" ixz="1.28440213111978E-05" iyy="0.00120575127811288" iyz="-5.96476693868777E-06" izz="0.00111303075718569" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint3" type="revolute">
        <origin xyz="0 0 -0.1294" rpy="1.57 0 1.57" />
        <parent link="l_Link2" />
        <child link="l_Link3" />
        <axis xyz="0 0 1" />
        <limit lower="-0.571" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="l_Link4">
        <inertial>
            <origin xyz="-0.0281665464550487 -1.66533453693773E-16 -9.35944287158152E-05" rpy="0 0 0" />
            <mass value="0.0199426877687591" />
            <inertia ixx="2.96538352859809E-06" ixy="3.79521102413356E-21" ixz="-1.86675439967538E-07" iyy="1.8765775426067E-05" iyz="-7.95551504209232E-22" izz="2.15819605045628E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link4.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link4.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint4" type="revolute">
        <origin xyz="-0.357 0 0" rpy="-1.5708 0 0" />
        <parent link="l_Link3" />
        <child link="l_Link4" />
        <axis xyz="0 0 1" />
        <limit lower="-1.5" upper="1.5" effort="100" velocity="1" />
    </joint>

    <link name="l_Link4_mimic">
        <inertial>
            <origin xyz="-0.0752004725328294 -0.000810778625682906 -1.30612599973379E-05" rpy="0 0 0" />
            <mass value="0.242656425906805" />
            <inertia ixx="6.62209332631248E-05" ixy="2.45810100872619E-08" ixz="-5.473149526733E-09" iyy="0.000356454991180688" iyz="1.5286496252015E-09" izz="0.000388234421821599" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link4_mimic.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link4_mimic.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint4_mimic" type="continuous">
        <origin xyz="-0.06 0 -0.00225" rpy="0 0 0" />
        <parent link="l_Link4" />
        <child link="l_Link4_mimic" />
        <axis xyz="0 0 -1" />
        <mimic joint="l_joint4" multiplier="-1" offset="0" />
    </joint>

    <link name="l_Link5">
        <inertial>
            <origin xyz="2.47592351859671E-05 -4.74781231818433E-06 -0.00229471749597726" rpy="0 0 0" />
            <mass value="0.0372662426645849" />
            <inertia ixx="4.2427624945891E-06" ixy="3.06377692898655E-11" ixz="1.14548420453011E-13" iyy="3.95071590836367E-06" iyz="2.75908401990349E-13" izz="7.1234820941962E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link5.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link5.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint5" type="revolute">
        <origin xyz="-0.2525 0 0" rpy="1.5708 0 0" />
        <parent link="l_Link4_mimic" />
        <child link="l_Link5" />
        <axis xyz="0 0 1" />
        <limit lower="-1.7" upper="1.7" effort="100" velocity="1" />
    </joint>

    <link name="l_Link6">
        <inertial>
            <origin xyz="-0.0389356549006475 1.05351387326702E-05 0.000816035681463934" rpy="0 0 0" />
            <mass value="0.0980000337957391" />
            <inertia ixx="2.58823491197887E-05" ixy="2.98957026700156E-09" ixz="1.00860392852907E-08" iyy="3.55841578211862E-05" iyz="-2.93398232365059E-08" izz="3.99613629951895E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link6.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link6.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint6" type="revolute">
        <origin xyz="0 0 0" rpy="-1.5708 0 0" />
        <parent link="l_Link5" />
        <child link="l_Link6" />
        <axis xyz="0 0 1" />
        <limit lower="-1.9" upper="1.9" effort="100" velocity="1" />
    </joint>

    <link name="l_Link7">
        <inertial>
            <origin xyz="0.00362870820555683 0.000104825649319629 0.167500322719216" rpy="0 0 0" />
            <mass value="0.174945152499489" />
            <inertia ixx="4.77591666888126E-05" ixy="-4.67469617699992E-06" ixz="-2.02856506736587E-07" iyy="5.72609701459943E-05" iyz="-1.92045408417679E-06" izz="5.88929475426381E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 -0.1385" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link7.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/l_Link7.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_joint7" type="revolute">
        <origin xyz="-0.1385 0 0" rpy="3.1416 1.5708 0" />
        <parent link="l_Link6" />
        <child link="l_Link7" />
        <axis xyz="0 0 1" />
        <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="l_hand_base">
        <inertial>
            <origin xyz="-0.0021204 0.0037993 0.056512" rpy="0 0 0" />
            <mass value="0.1416" />
            <inertia ixx="0.00012114" ixy="-1.3823E-06" ixz="3.5219E-06" iyy="0.00019604" iyz="4.8261E-07" izz="9.5758E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/base_link.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/base_link.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_end_effector" type="fixed">
        <origin xyz="0.0015 0 0" rpy="0 0 -1.57" />
        <parent link="l_Link7" />
        <child link="l_hand_base" />
        <axis xyz="0 0 1" />
    </joint>

    <link name="l_picky_MC">
        <inertial>
            <origin xyz="0.0047869 -4.0757E-06 0.00013142" rpy="0 0 0" />
            <mass value="0.00076708" />
            <inertia  ixx="4.5174E-08"  ixy="-2.0015E-11" ixz="-2.3083E-09"  iyy="4.3355E-08"  iyz="-3.0679E-13" izz="1.8273E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_picky_MCP_FE" type="revolute">
        <origin xyz="0.034478 0.0069265 0.10145" rpy="1.5708 1.5708 0" />
        <parent link="l_hand_base" />
        <child link="l_picky_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="100" velocity="1" />
    </joint>
    
    <link name="l_picky_PP">
        <inertial>
            <origin xyz="-0.022096 6.1099E-05 -0.00050608" rpy="0 0 0" />
            <mass value="0.011681" />
            <inertia ixx="7.2897E-07" ixy="-8.7868E-09" ixz="-7.549E-08" iyy="2.4233E-06" iyz="1.2667E-09" izz="2.3185E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_picky_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="l_picky_MC" />
        <child link="l_picky_PP" />
        <axis xyz="0 0 1" />
        <limit lower="-0.1745" upper="0.1745" effort="100" velocity="1" />
    </joint>

    <link name="l_picky_MP">
        <inertial>
            <origin xyz="-0.012777 0.0030509 -0.00094605" rpy="0 0 0" />
            <mass value="0.0049366" />
            <inertia ixx="2.8454E-07" ixy="-1.9432E-08" ixz="4.6897E-08" iyy="4.0039E-07" iyz="-1.0361E-09" izz="4.0154E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_picky_PIP" type="revolute">
        <origin xyz="-0.044075 -0.00275 0" rpy="0 0 0" />
        <parent link="l_picky_PP" />
        <child link="l_picky_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="100" velocity="1" />
    </joint>

    <link name="l_picky_DP">
        <inertial>
            <origin xyz="-0.0062576 0.00091011 -0.0045714" rpy="0 0 0" />
            <mass value="0.010293" />
            <inertia ixx="4.2546E-06" ixy="-4.8997E-06" ixz="-5.4954E-06" iyy="1.6236E-05" iyz="-1.9591E-06" izz="1.5956E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link1-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_picky_DIP" type="revolute">
        <origin xyz="-0.025098 0.00275 0.0050003" rpy="0 0 0" />
        <parent link="l_picky_MP" />
        <child link="l_picky_DP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.379" effort="100" velocity="1" />
    </joint>

    <link name="l_ring_MC">
        <inertial>
            <origin xyz="0.0047869 -4.0757E-06 0.00013142" rpy="0 0 0" />
            <mass value="0.00076708" />
            <inertia ixx="4.5174E-08" ixy="-2.0015E-11" ixz="-2.3083E-09" iyy="4.3355E-08" iyz="-3.0679E-13" izz="1.8273E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_ring_MCP_FE" type="revolute">
        <origin xyz="0.012078 0.0069265 0.11045" rpy="1.5708 1.5708 0" />
        <parent link="l_hand_base" />
        <child link="l_ring_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="100" velocity="1" />
    </joint>

    <link name="l_ring_PP">
        <inertial>
            <origin xyz="-0.022096 6.1099E-05 -0.00050608" rpy="0 0 0" />
            <mass value="0.011681" />
            <inertia ixx="7.2897E-07" ixy="-8.7868E-09" ixz="-7.549E-08" iyy="2.4233E-06" iyz="1.2667E-09" izz="2.3185E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_ring_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="l_ring_MC" />
        <child link="l_ring_PP" />
        <axis xyz="0 0 1" />
        <limit lower="-0.1745" upper="0.1745" effort="100" velocity="1" />
    </joint>

    <link name="l_ring_MP">
        <inertial>
            <origin xyz="-0.012777 0.0030509 -0.00094605" rpy="0 0 0" />
            <mass value="0.0049366" />
            <inertia ixx="2.8454E-07" ixy="-1.9432E-08" ixz="4.6897E-08" iyy="4.0039E-07" iyz="-1.0361E-09" izz="4.0154E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_ring_PIP" type="revolute">
        <origin xyz="-0.044075 -0.00275 0" rpy="0 0 0" />
        <parent link="l_ring_PP" />
        <child link="l_ring_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="100" velocity="1" />
    </joint>

    <link name="l_ring_DP">
        <inertial>
            <origin xyz="-0.0062568 0.0009104 -0.0045711" rpy="0 0 0" />
            <mass value="0.010293" />
            <inertia ixx="4.2559E-06" ixy="-4.9013E-06" ixz="-5.4971E-06" iyy="1.6241E-05" iyz="-1.9597E-06" izz="1.596E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link2-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_ring_DIP" type="revolute">
        <origin xyz="-0.025098 0.00275 0.0050003" rpy="0 0 0" />
        <parent link="l_ring_MP" />
        <child link="l_ring_DP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.379" effort="100" velocity="1" />
    </joint>

    <link name="l_middle_MC">
        <inertial>
            <origin xyz="0.0047869 -4.0757E-06 0.00013142" rpy="0 0 0" />
            <mass value="0.00076708" />
            <inertia ixx="4.5174E-08" ixy="-2.0015E-11" ixz="-2.3083E-09" iyy="4.3355E-08" iyz="-3.0679E-13" izz="1.8273E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_middle_MCP_FE" type="revolute">
        <origin xyz="-0.010322 0.0069265 0.11945" rpy="1.5708 1.5708 0" />
        <parent link="l_hand_base" />
        <child link="l_middle_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="100" velocity="1" />
    </joint>

    <link name="l_middle_PP">
        <inertial>
            <origin xyz="-0.022096 6.1099E-05 -0.00050608" rpy="0 0 0" />
            <mass value="0.011681" />
            <inertia ixx="7.2897E-07" ixy="-8.7868E-09" ixz="-7.549E-08" iyy="2.4233E-06" iyz="1.2667E-09" izz="2.3185E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_middle_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="l_middle_MC" />
        <child link="l_middle_PP" />
        <axis xyz="0 0 1" />
        <limit lower="-0.1745" upper="0.1745" effort="100" velocity="1" />
    </joint>

    <link name="l_middle_MP">
        <inertial>
            <origin xyz="-0.012777 0.0030509 -0.00094605" rpy="0 0 0" />
            <mass value="0.0049366" />
            <inertia ixx="2.8454E-07" ixy="-1.9432E-08" ixz="4.6897E-08" iyy="4.0039E-07" iyz="-1.0361E-09" izz="4.0154E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_middle_PIP" type="revolute">
        <origin xyz="-0.044075 -0.00275 0" rpy="0 0 0" />
        <parent link="l_middle_PP" />
        <child link="l_middle_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="100" velocity="1" />
    </joint>

    <link name="l_middle_DP">
        <inertial>
            <origin xyz="-0.0062575 0.00091012 -0.0045714" rpy="0 0 0" />
            <mass value="0.010293" />
            <inertia ixx="4.2547E-06" ixy="-4.8998E-06" ixz="-5.4955E-06" iyy="1.6236E-05" iyz="-1.9591E-06" izz="1.5956E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
              <mesh filename="package://robot_controller/description/hand_left/meshes/Link3-3.STL" />
          </geometry>
        </collision>
    </link>

    <joint name="l_middle_DIP" type="revolute">
        <origin xyz="-0.025098 0.00275 0.0050003" rpy="0 0 0" />
        <parent link="l_middle_MP" />
        <child link="l_middle_DP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.379" effort="100" velocity="1" />
    </joint>

    <link name="l_index_MC">
        <inertial>
            <origin xyz="0.0047869 -4.0757E-06 0.00013142" rpy="0 0 0" />
            <mass value="0.00076708" />
            <inertia ixx="4.5174E-08" ixy="-2.0015E-11" ixz="-2.3083E-09" iyy="4.3355E-08" iyz="-3.0679E-13" izz="1.8273E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4.STL" />
            </geometry>
            <material name="">
              <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_index_MCP_FE" type="revolute">
        <origin xyz="-0.032722 0.0069265 0.11045" rpy="1.5708 1.5708 0" />
        <parent link="l_hand_base" />
        <child link="l_index_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="100" velocity="1" />
    </joint>

    <link name="l_index_PP">
        <inertial>
            <origin xyz="-0.022096 6.1099E-05 -0.00050608" rpy="0 0 0" />
            <mass value="0.011681" />
            <inertia ixx="7.2897E-07" ixy="-8.7868E-09" ixz="-7.549E-08" iyy="2.4233E-06" iyz="1.2667E-09" izz="2.3185E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_index_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="l_index_MC" />
        <child link="l_index_PP" />
        <axis xyz="0 0 1" />
        <limit lower="-0.1745" upper="0.1745" effort="100" velocity="1" />
    </joint>

    <link name="l_index_MP">
        <inertial>
            <origin xyz="-0.012777 0.0030509 -0.00094605" rpy="0 0 0" />
            <mass value="0.0049366" />
            <inertia ixx="2.8454E-07" ixy="-1.9432E-08" ixz="4.6897E-08" iyy="4.0039E-07" iyz="-1.0361E-09" izz="4.0154E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_index_PIP" type="revolute">
        <origin xyz="-0.044075 -0.00275 0" rpy="0 0 0" />
        <parent link="l_index_PP" />
        <child link="l_index_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="100" velocity="1" />
    </joint>

    <link name="l_index_DP">
        <inertial>
            <origin xyz="-0.0062577 0.00091006 -0.0045715" rpy="0 0 0" />
            <mass value="0.010293" />
            <inertia ixx="4.2544E-06" ixy="-4.8995E-06" ixz="-5.4951E-06" iyy="1.6235E-05" iyz="-1.959E-06" izz="1.5955E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link4-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_index_DIP" type="revolute">
        <origin xyz="-0.025098 0.00275 0.0050003" rpy="0 0 0" />
        <parent link="l_index_MP" />
        <child link="l_index_DP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.379" effort="100" velocity="1" />
    </joint>

    <link name="l_thumb_MC">
        <inertial>
            <origin xyz="4.0754E-06 0.0047869 0.00013142" rpy="0 0 0" />
            <mass value="0.00076708" />
            <inertia ixx="4.3355E-08" ixy="2.0011E-11" ixz="3.1542E-13" iyy="4.5174E-08" iyz="-2.3083E-09" izz="1.8273E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_thumb_MCP_FE" type="revolute">
        <origin xyz="-0.032422 -0.017073 0.040951" rpy="0.0038428 1.5708 0" />
        <parent link="l_hand_base" />
        <child link="l_thumb_MC" />
        <axis xyz="-1 0 0" />
        <limit lower="-1.57" upper="0.525" effort="100" velocity="1" />
    </joint>

    <link name="l_thumb_PP">
        <inertial>
            <origin xyz="-0.016358 -0.020593 0.0014703" rpy="0 0 0" />
            <mass value="0.0067566" />
            <inertia ixx="9.6985E-07" ixy="-3.6597E-07" ixz="2.1307E-08" iyy="1.015E-06" iyz="-3.3027E-08" izz="1.3611E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_thumb_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="l_thumb_MC" />
        <child link="l_thumb_PP" />
        <axis xyz="0 0 -1" />
        <limit lower="-0.349" upper="0.524" effort="100" velocity="1" />
    </joint>

    <link name="l_thumb_MP">
        <inertial>
            <origin xyz="-0.012375 0.003525 -0.0011451" rpy="0 0 0" />
            <mass value="0.0019233" />
            <inertia ixx="1.2936E-07" ixy="-8.4109E-09" ixz="1.7921E-08" iyy="1.633E-07" iyz="-7.3968E-10" izz="1.7847E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_thumb_PIP" type="revolute">
        <origin xyz="-0.031796 -0.038811 0.0024025" rpy="-0.87266 0 0.69813" />
        <parent link="l_thumb_PP" />
        <child link="l_thumb_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="100" velocity="1" />
    </joint>

    <link name="l_thumb_DP">
        <inertial>
            <origin xyz="-0.0043987 0.00091311 -0.0038257" rpy="0 0 0" />
            <mass value="0.011525" />
            <inertia ixx="6.1376E-06" ixy="-5.8956E-06" ixz="-1.1344E-05" iyy="3.4245E-05" iyz="-2.302E-06" izz="3.1105E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_left/meshes/Link5-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="l_thumb_DIP" type="revolute">
        <origin xyz="-0.025096 0.00315 0.005" rpy="0 0 0" />
        <parent link="l_thumb_MP" />
        <child link="l_thumb_DP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.36" effort="100" velocity="1" />
    </joint>

    <link name="r_Link1">
        <inertial>
            <origin xyz="6.76867879206844E-05 -0.000903931135904396 -3.56608652760659E-05" rpy="0 0 0" />
            <mass value="0.431056457943263" />
            <inertia ixx="0.000302507987001375" ixy="-2.65452089122247E-12" ixz="-8.56215025520301E-08" iyy="0.000293870632390727" iyz="-3.19657194658115E-07" izz="0.000443817230586722" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
              <mesh filename="package://robot_controller/description/meshes/r_Link1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint1" type="revolute">
        <origin xyz="0 -0.16495 0" rpy="0 0 0" />
        <parent link="base_arm" />
        <child link="r_Link1" />
        <axis xyz="0 0 1" />
        <limit lower="-2.02" upper="2.02" effort="100" velocity="1" />
    </joint>

    <link name="r_Link2">
        <inertial>
            <origin xyz="0.00543828327872342 3.65030081850293E-05 -0.100373714270726" rpy="0 0 0" />
            <mass value="0.590402422555339" />
            <inertia ixx="0.000656083218952701" ixy="5.94191053830923E-07" ixz="1.87408981544293E-05" iyy="0.000554692204253716" iyz="6.06730816304197E-07" izz="0.000552406206489923" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="-3.14 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
              <mesh filename="package://robot_controller/description/meshes/r_Link2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint2" type="revolute">
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <parent link="r_Link1" />
        <child link="r_Link2" />
        <axis xyz="0 0 1" />
        <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="r_Link3">
        <inertial>
            <origin xyz="-0.208209353751583 -0.000661214297171031 0.000149105614622336" rpy="0 0 0" />
            <mass value="0.50505895689041" />
            <inertia ixx="0.000216923846591363" ixy="-2.10730757014553E-05" ixz="1.28440213096489E-05" iyy="0.00120575127812064" iyz="-5.96476694296668E-06" izz="0.00111303075719106" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint3" type="revolute">
        <origin xyz="0 0 0.1295" rpy="-1.5708 0 1.5708" />
        <parent link="r_Link2" />
        <child link="r_Link3" />
        <axis xyz="0 0 1" />
        <limit lower="-0.571" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="r_Link4">
        <inertial>
            <origin xyz="-0.0281665464550493 5.55111512312578E-17 9.35944287152601E-05" rpy="0 0 0" />
            <mass value="0.0199426877687593" />
            <inertia ixx="2.96538352859811E-06" ixy="1.35581460135326E-20" ixz="1.86675439967549E-07" iyy="1.87657754260671E-05" iyz="5.70925821339644E-22" izz="2.1581960504563E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link4.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link4.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint4" type="revolute">
        <origin xyz="-0.357 0 0" rpy="1.5708 0 0" />
        <parent link="r_Link3" />
        <child link="r_Link4" />
        <axis xyz="0 0 1" />
        <limit lower="-1.5" upper="1.5" effort="100" velocity="1" />
    </joint>

    <link name="r_Link4_mimic">
        <inertial>
            <origin xyz="-0.0750923237562536 -0.000831115580014974 -6.65894588747218E-06" rpy="0 0 0" />
            <mass value="0.242501585243888" />
            <inertia ixx="6.62204329953082E-05" ixy="2.45785261026615E-08" ixz="5.47314952678275E-09" iyy="0.000356454753003177" iyz="1.5286496252308E-09" izz="0.000388233921553782" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link4_mimic.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link4_mimic.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint4_mimic" type="continuous">
        <origin xyz="-0.06 0 0.00225" rpy="0 0 0" />
        <parent link="r_Link4" />
        <child link="r_Link4_mimic" />
        <axis xyz="0 0 1" />
        <mimic joint="r_joint4" multiplier="1" offset="0" />
    </joint>

    <link name="r_Link5">
        <inertial>
            <origin xyz="-2.475923518791E-05 4.74781231835086E-06 -0.00229471749597882" rpy="0 0 0" />
            <mass value="0.0372662426645848" />
            <inertia ixx="4.24276249458909E-06" ixy="3.0637769290948E-11" ixz="-1.14548063193663E-13" iyy="3.95071590836366E-06" iyz="-2.75908404555735E-13" izz="7.12348209419618E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link5.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link5.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint5" type="revolute">
        <origin xyz="-0.2525 0 0" rpy="-1.5708 0 0" />
        <parent link="r_Link4_mimic" />
        <child link="r_Link5" />
        <axis xyz="0 0 1" />
        <limit lower="-1.7" upper="1.7" effort="100" velocity="1" />
    </joint>

    <link name="r_Link6">
        <inertial>
            <origin xyz="-0.0389178609640031 1.96926696393041E-05 0.000816350189191073" rpy="0 0 0" />
            <mass value="0.097962278223917" />
            <inertia ixx="2.58822870387763E-05" ixy="2.98957026701835E-09" ixz="1.00860392852922E-08" iyy="3.55839964031528E-05" iyz="-2.9339823236487E-08" izz="3.99612015771559E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link6.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link6.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint6" type="revolute">
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <parent link="r_Link5" />
        <child link="r_Link6" />
        <axis xyz="0 0 1" />
        <limit lower="-1.9" upper="1.9" effort="100" velocity="1" />
    </joint>

    <link name="r_Link7">
        <inertial>
            <origin xyz="0.00354796455127288 0.000771530873334259 0.167500227086532" rpy="0 0 0" />
            <mass value="0.17494608086546" />
            <inertia ixx="4.97736724967938E-05" ixy="-6.07737424785576E-06" ixz="1.53563941129149E-07" iyy="5.52450693003139E-05" iyz="-1.92528841604845E-06" izz="5.88920949259117E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0.134" rpy="0 3.14 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/meshes/r_Link7.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
              <mesh filename="package://robot_controller/description/meshes/r_Link7.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_joint7" type="revolute">
        <origin xyz="-0.134 0 0.0" rpy="1.57 0 1.57" />
        <parent link="r_Link6" />
        <child link="r_Link7" />
        <axis xyz="0 0 1" />
        <limit lower="-3.14" upper="3.14" effort="100" velocity="1" />
    </joint>

    <link name="r_hand_base">
        <inertial>
            <origin xyz="-0.067296 0.00086821 -0.0073265" rpy="0 0 0" />
            <mass value="0.28552" />
            <inertia ixx="8.0859E-05" ixy="4.6301E-06" ixz="-2.2398E-06" iyy="6.649E-05" iyz="-6.3857E-07" izz="0.00013203" />
        </inertial>
        <visual>
            <origin xyz="0 0.001 0.001" rpy="0 -1.5708 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/base_link.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/base_link.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_end_effector" type="fixed">
        <origin xyz="0 0 0" rpy="0 -1.5708 1.5708" />
        <parent link="r_Link7" />
        <child link="r_hand_base" />
        <axis xyz="0 0 1" />
    </joint>

    <link name="r_picky_MC">
        <inertial>
            <origin xyz="0.0023371 -1.417E-06 -8.2686E-05" rpy="0 0 0" />
            <mass value="0.0050019" />
            <inertia ixx="2.1605E-07" ixy="-4.967E-11" ixz="-4.5802E-09" iyy="1.8593E-07" iyz="1.5435E-12" izz="1.1246E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link1.STL" />
            </geometry>
        </collision>
    </link>
    
    <joint name="r_picky_MCP_FE" type="revolute">
        <origin xyz="-0.10626 -0.034139 -0.0079018" rpy="0 0 0" />
        <parent link="r_hand_base" />
        <child link="r_picky_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="15" velocity="1" />
    </joint>

    <link name="r_picky_PP">
        <inertial>
            <origin xyz="-0.024612 -1.1602E-05 -0.00027323" rpy="0 0 0" />
            <mass value="0.018989" />
            <inertia ixx="9.4752E-07" ixy="3.0811E-09" ixz="6.8694E-09" iyy="9.9219E-07" iyz="2.1926E-10" izz="9.0388E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link1-1.STL" />
            </geometry>
            <material name="">
              <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
              <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link1-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_picky_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_picky_MC" />
        <child link="r_picky_PP" />
        <axis xyz="0 0 -1" />
        <limit lower="-0.1745" upper="0.1745" effort="15" velocity="1" />
    </joint>

    <link name="r_picky_MP">
        <inertial>
            <origin xyz="-0.013142 -0.0001805 -0.0010017" rpy="0 0 0" />
            <mass value="0.010003" />
            <inertia ixx="5.698E-07" ixy="2.4196E-08" ixz="7.4807E-08" iyy="7.1674E-07" iyz="6.4938E-10" izz="7.1618E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link1-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link1-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_picky_PIP" type="revolute">
        <origin xyz="-0.044075 0 0" rpy="0 0 0" />
        <parent link="r_picky_PP" />
        <child link="r_picky_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.57" effort="15" velocity="1" />
    </joint>

    <link name="r_picky_DP">
        <inertial>
            <origin xyz="0.0040415 6.2837E-08 -0.0045481" rpy="0 0 0" />
            <mass value="0.0014847" />
            <inertia ixx="2.6654E-08" ixy="-1.5551E-13" ixz="-1.0371E-09" iyy="2.3165E-08" iyz="-8.4767E-13" izz="2.5093E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link1-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0"  rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link1-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_picky_DIP" type="revolute">
      <origin xyz="-0.025098 0 0.0050003" rpy="0 0 0" />
      <parent link="r_picky_MP" />
      <child link="r_picky_DP" />
      <axis xyz="0 -1 0" />
      <limit lower="-1.379" upper="0" effort="15" velocity="1" />
    </joint>

    <link name="r_gelsight_picky">
        <inertial>
            <origin xyz="0.0031311 0.0039236 0.0070303" rpy="0 0 0" />
            <mass value="0.0023239" />
            <inertia ixx="2.2184E-06" ixy="2.6638E-06" ixz="-2.9419E-06" iyy="8.6296E-06" iyz="1.061E-06" izz="8.4598E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
              <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
          </geometry>
        </collision>
    </link>

    <joint name="r_picky_TIP_gelsight" type="fixed">
        <origin xyz="0 -0.001765 0.00175" rpy="-3.1416 0 -3.1416" />
        <parent link="r_picky_DP" />
        <child link="r_gelsight_picky" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_camera_link_picky"/>
      
    <joint name="r_picky_TIP_camera" type="fixed">
        <origin xyz="0.0036365 0.0017649 0.0084125" rpy="-3.1416 0.95481 -3.1416" />
        <parent link="r_gelsight_picky" />
        <child link="r_camera_link_picky" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_picky">
        <inertial>
            <origin xyz="0.014064 0.0017654 0.0026187" rpy="0 0 0" />
            <mass value="0.00092058" />
            <inertia ixx="1.4869E-08" ixy="-2.9812E-13" ixz="-8.1097E-09" iyy="2.7055E-08" iyz="3.3046E-12" izz="3.2852E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerkz.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomer.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_picky_TIP_elastomer" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_gelsight_picky" />
        <child link="r_elastomer_picky" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_tip_picky"/>
      
    <joint name="r_picky_TIP_elastomer_tip" type="fixed">
        <origin xyz="0.016231 0.0017649 -0.00010532" rpy="-3.1416 0.37247 -3.1416" />
        <parent link="r_gelsight_picky" />
        <child link="r_elastomer_tip_picky" />
        <axis xyz="0 0 0" />
    </joint>
    
    <link name="r_ring_MC">
        <inertial>
            <origin xyz="0.0023371 -1.417E-06 -8.2686E-05" rpy="0 0 0" />
            <mass value="0.0050019" />
            <inertia ixx="2.1605E-07" ixy="-4.967E-11" ixz="-4.5802E-09" iyy="1.8593E-07" iyz="1.5435E-12" izz="1.1246E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_MCP_FE" type="revolute">
        <origin xyz="-0.11526 -0.011739 -0.0079018" rpy="0 0 0" />
        <parent link="r_hand_base" />
        <child link="r_ring_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="15" velocity="1" />
    </joint>

    <link name="r_ring_PP">
        <inertial>
            <origin xyz="-0.024612 -1.1602E-05 -0.00027323" rpy="0 0 0" />
            <mass value="0.018989" />
            <inertia ixx="9.4752E-07" ixy="3.0811E-09" ixz="6.8694E-09" iyy="9.9219E-07" iyz="2.1926E-10" izz="9.0388E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link2-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link2-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_ring_MC" />
        <child link="r_ring_PP" />
        <axis xyz="0 0 -1" />
        <limit lower="-0.1745" upper="0.1745" effort="15" velocity="1" />
    </joint>

    <link name="r_ring_MP">
        <inertial>
            <origin xyz="-0.013142 -0.0001805 -0.0010017" rpy="0 0 0" />
            <mass value="0.010003" />
            <inertia ixx="5.698E-07" ixy="2.4196E-08" ixz="7.4807E-08" iyy="7.1674E-07" iyz="6.4938E-10" izz="7.1618E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link2-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link2-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_PIP" type="revolute">
        <origin xyz="-0.044075 0 0" rpy="0 0 0" />
        <parent link="r_ring_PP" />
        <child link="r_ring_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0.1" upper="1.57" effort="15" velocity="1" />
    </joint>

    <link name="r_ring_DP">
        <inertial>
            <origin xyz="0.0040415 6.2839E-08 -0.0045481" rpy="0 0 0" />
            <mass value="0.0014847" />
            <inertia ixx="2.6654E-08" ixy="-1.5553E-13" ixz="-1.0371E-09" iyy="2.3165E-08" iyz="-8.4768E-13" izz="2.5093E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link2-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link2-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_DIP" type="revolute">
        <origin xyz="-0.025098 0 0.0050003" rpy="0 0 0" />
        <parent link="r_ring_MP" />
        <child link="r_ring_DP" />
        <axis xyz="0 -1 0" />
        <limit lower="-1.379" upper="0" effort="15" velocity="1" />
    </joint>

    <link name="r_gelsight_ring">
        <inertial>
            <origin xyz="0.0031311 0.0039236 0.0070303" rpy="0 0 0" />
            <mass value="0.0023239" />
            <inertia ixx="2.2184E-06" ixy="2.6638E-06" ixz="-2.9419E-06" iyy="8.6296E-06" iyz="1.061E-06" izz="8.4598E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_TIP_gelsight" type="fixed">
        <origin xyz="0 -0.001765 0.00175" rpy="3.1416 0 3.1416" />
        <parent link="r_ring_DP" />
        <child link="r_gelsight_ring" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_camera_link_ring"/>
      
    <joint name="r_ring_TIP_camera" type="fixed">
        <origin xyz="0.0036365 0.0017649 0.0084125" rpy="-3.1416 0.95481 -3.1416" />
        <parent link="r_gelsight_ring" />
        <child link="r_camera_link_ring" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_ring">
        <inertial>
            <origin xyz="0.014064 0.0017654 0.0026187" rpy="0 0 0" />
            <mass value="0.00092058" />
            <inertia ixx="1.4869E-08" ixy="-2.9812E-13" ixz="-8.1097E-09" iyy="2.7055E-08" iyz="3.3046E-12" izz="3.2852E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerkz.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomer.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_ring_TIP_elastomer" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_gelsight_ring" />
        <child link="r_elastomer_ring" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_tip_ring"/>
      
    <joint name="r_ring_TIP_elastomer_tip" type="fixed">
        <origin xyz="0.016231 0.0017649 -0.00010532" rpy="-3.1416 0.37247 -3.1416" />
        <parent link="r_gelsight_ring" />
        <child link="r_elastomer_tip_ring" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_middle_MC">
        <inertial>
            <origin xyz="0.0023371 -1.417E-06 -8.2686E-05" rpy="0 0 0" />
            <mass value="0.0050019" />
            <inertia ixx="2.1605E-07" ixy="-4.967E-11" ixz="-4.5802E-09" iyy="1.8593E-07" iyz="1.5435E-12" izz="1.1246E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_MCP_FE" type="revolute">
        <origin xyz="-0.12426 0.010661 -0.0079018" rpy="0 0 0" />
        <parent link="r_hand_base" />
        <child link="r_middle_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="15" velocity="1" />
    </joint>

    <link name="r_middle_PP">
        <inertial>
            <origin xyz="-0.024612 -1.1602E-05 -0.00027323" rpy="0 0 0" />
            <mass value="0.018989" />
            <inertia ixx="9.4752E-07" ixy="3.0811E-09" ixz="6.8694E-09" iyy="9.9219E-07" iyz="2.1926E-10" izz="9.0388E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link3-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link3-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_middle_MC" />
        <child link="r_middle_PP" />
        <axis xyz="0 0 -1" />
        <limit lower="-0.1745" upper="0.1745" effort="15" velocity="1" />
    </joint>

    <link name="r_middle_MP">
        <inertial>
            <origin xyz="-0.013142 -0.0001805 -0.0010017" rpy="0 0 0" />
            <mass value="0.010003" />
            <inertia ixx="5.698E-07" ixy="2.4196E-08" ixz="7.4807E-08" iyy="7.1673E-07" iyz="6.4947E-10" izz="7.1618E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link3-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link3-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_PIP" type="revolute">
      <origin xyz="-0.044075 0 0" rpy="0 0 0" />
      <parent link="r_middle_PP" />
      <child link="r_middle_MP" />
      <axis xyz="0 1 0" />
      <limit lower="0.1" upper="1.57" effort="15" velocity="1" />
    </joint>

    <link name="r_middle_DP">
        <inertial>
            <origin xyz="0.0040415 6.2839E-08 -0.0045481" rpy="0 0 0" />
            <mass value="0.0014847" />
            <inertia ixx="2.6654E-08" ixy="-1.5553E-13" ixz="-1.0371E-09" iyy="2.3165E-08" iyz="-8.4768E-13" izz="2.5093E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link3-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link3-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_DIP" type="revolute">
        <origin xyz="-0.025098 0 0.0050003" rpy="0 0 0" />
        <parent link="r_middle_MP" />
        <child link="r_middle_DP" />
        <axis xyz="0 -1 0" />
        <limit lower="-1.379" upper="0" effort="15" velocity="1" />
    </joint>

    <link name="r_gelsight_middle">
        <inertial>
            <origin xyz="0.0031311 0.0039236 0.0070303" rpy="0 0 0" />
            <mass value="0.0023239" />
            <inertia ixx="2.2184E-06" ixy="2.6638E-06" ixz="-2.9419E-06" iyy="8.6296E-06" iyz="1.061E-06" izz="8.4598E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_TIP_gelsight" type="fixed">
        <origin xyz="0 -0.001765 0.00175" rpy="3.1416 0 3.1416" />
        <parent link="r_middle_DP" />
        <child link="r_gelsight_middle" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_camera_link_middle"/>
      
    <joint name="r_middle_TIP_camera" type="fixed">
        <origin xyz="0.0036365 0.0017649 0.0084125" rpy="-3.1416 0.95481 -3.1416" />
        <parent link="r_gelsight_middle" />
        <child link="r_camera_link_middle" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_middle">
        <inertial>
            <origin xyz="0.014064 0.0017654 0.0026187" rpy="0 0 0" />
            <mass value="0.00092058" />
            <inertia ixx="1.4869E-08" ixy="-2.9812E-13" ixz="-8.1097E-09" iyy="2.7055E-08" iyz="3.3046E-12" izz="3.2852E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerkz.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomer.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_middle_TIP_elastomer" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_gelsight_middle" />
        <child link="r_elastomer_middle" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_tip_middle"/>
      
    <joint name="r_middle_TIP_elastomer_tip" type="fixed">
        <origin xyz="0.016231 0.0017649 -0.00010532" rpy="-3.1416 0.37247 -3.1416" />
        <parent link="r_gelsight_middle" />
        <child link="r_elastomer_tip_middle" />
      <axis xyz="0 0 0" />
    </joint>

    <link name="r_index_MC">
        <inertial>
            <origin xyz="0.0023371 -1.417E-06 -8.2686E-05" rpy="0 0 0" />
            <mass value="0.0050019" />
            <inertia ixx="2.1605E-07" ixy="-4.967E-11" ixz="-4.5802E-09" iyy="1.8593E-07" iyz="1.5435E-12" izz="1.1246E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link4.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link4.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_MCP_FE" type="revolute">
        <origin xyz="-0.11526 0.033061 -0.0079018" rpy="0 0 0" />
        <parent link="r_hand_base" />
        <child link="r_index_MC" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.309" effort="15" velocity="1" />
    </joint>

    <link name="r_index_PP">
        <inertial>
            <origin xyz="-0.024612 -1.1602E-05 -0.00027323" rpy="0 0 0" />
            <mass value="0.018989" />
            <inertia ixx="9.4752E-07" ixy="3.0811E-09" ixz="6.8694E-09" iyy="9.9219E-07" iyz="2.1926E-10" izz="9.0388E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link4-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link4-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_index_MC" />
        <child link="r_index_PP" />
        <axis xyz="0 0 -1" />
        <limit lower="-0.1745" upper="0.1745" effort="15" velocity="1" />
    </joint>

    <link name="r_index_MP">
        <inertial>
            <origin xyz="-0.013142 -0.0001805 -0.0010017" rpy="0 0 0" />
            <mass value="0.010003" />
            <inertia ixx="5.698E-07" ixy="2.4196E-08" ixz="7.4807E-08" iyy="7.1673E-07" iyz="6.4947E-10" izz="7.1618E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link4-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link4-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_PIP" type="revolute">
        <origin xyz="-0.044075 0 0" rpy="0 0 0" />
        <parent link="r_index_PP" />
        <child link="r_index_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0.1" upper="1.57" effort="15" velocity="1" />
    </joint>

    <link name="r_index_DP">
        <inertial>
            <origin xyz="0.0040415 6.2839E-08 -0.0045481" rpy="0 0 0" />
            <mass value="0.0014847" />
            <inertia ixx="2.6654E-08" ixy="-1.5553E-13" ixz="-1.0371E-09" iyy="2.3165E-08" iyz="-8.4768E-13" izz="2.5093E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link4-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link4-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_DIP" type="revolute">
        <origin xyz="-0.025098 0 0.0050003" rpy="0 0 0" />
        <parent link="r_index_MP" />
        <child link="r_index_DP" />
        <axis xyz="0 -1 0" />
        <limit lower="-1.379" upper="0" effort="15" velocity="1" />
    </joint>

    <link name="r_gelsight_index">
        <inertial>
            <origin xyz="0.0031311 0.0039236 0.0070303" rpy="0 0 0" />
            <mass value="0.0023239" />
            <inertia ixx="2.2184E-06" ixy="2.6638E-06" ixz="-2.9419E-06" iyy="8.6296E-06" iyz="1.061E-06" izz="8.4598E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_finger.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_TIP_gelsight" type="fixed">
        <origin xyz="0 -0.001765 0.00175" rpy="3.1416 0 3.1416" />
        <parent link="r_index_DP" />
        <child link="r_gelsight_index" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_camera_link_index"/>
      
    <joint name="r_index_TIP_camera" type="fixed">
        <origin xyz="0.0036365 0.0017649 0.0084125" rpy="-3.1416 0.95481 -3.1416" />
        <parent link="r_gelsight_index" />
        <child link="r_camera_link_index" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_index">
        <inertial>
            <origin xyz="0.014064 0.0017654 0.0026187" rpy="0 0 0" />
            <mass value="0.00092058" />
            <inertia ixx="1.4869E-08" ixy="-2.9812E-13" ixz="-8.1097E-09" iyy="2.7055E-08" iyz="3.3046E-12" izz="3.2852E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerkz.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomer.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_index_TIP_elastomer" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_gelsight_index" />
        <child link="r_elastomer_index" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_tip_index"/>
      
    <joint name="r_index_TIP_elastomer_tip" type="fixed">
        <origin xyz="0.016231 0.0017649 -0.00010532" rpy="-3.1416 0.37247 -3.1416" />
        <parent link="r_gelsight_index" />
        <child link="r_elastomer_tip_index" />
        <axis xyz="0 0 0" />
    </joint>


    <link name="r_thumb_MC">
        <inertial>
            <origin xyz="1.7652E-07 -0.0022879 -0.00048469" rpy="0 0 0" />
            <mass value="0.0050018" />
            <inertia ixx="1.8591E-07" ixy="4.7121E-11" ixz="1.0685E-11" iyy="2.1134E-07" iyz="2.2024E-08" izz="1.1715E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link5.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link5.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_MCP_FE" type="revolute">
        <origin xyz="-0.04576 0.032759 0.016098" rpy="0 0 0" />
        <parent link="r_hand_base" />
        <child link="r_thumb_MC" />
        <axis xyz="-1 0 0" />
        <limit lower="-1.92" upper="0.175" effort="15" velocity="1" />
    </joint>

    <link name="r_thumb_PP">
        <inertial>
            <origin xyz="-0.017109 -0.0012119 0.022184" rpy="0 0 0" />
            <mass value="0.025202" />
            <inertia ixx="3.2968E-06" ixy="2.3853E-08" ixz="1.3781E-06" iyy="4.8369E-06" iyz="-1.4536E-07" izz="3.5328E-06" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link5-1.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link5-1.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_MCP_AA" type="revolute">
        <origin xyz="0 0 0" rpy="-1.3963 0 0" />
        <parent link="r_thumb_MC" />
        <child link="r_thumb_PP" />
        <axis xyz="0 -1 0" />
        <limit lower="-0.349" upper="0.524" effort="15" velocity="1" />
    </joint>

    <link name="r_thumb_MP">
        <inertial>
            <origin xyz="-0.012377 -0.00037916 -0.0011474" rpy="0 0 0" />
            <mass value="0.0054035" />
            <inertia ixx="3.6347E-07" ixy="2.3556E-08" ixz="5.0379E-08" iyy="4.5902E-07" iyz="2.2446E-09" izz="5.0162E-07" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link5-2.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link5-2.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_PIP" type="revolute">
        <origin xyz="-0.033091 0 0.037267" rpy="2.4435 0.69813 0" />
        <parent link="r_thumb_PP" />
        <child link="r_thumb_MP" />
        <axis xyz="0 1 0" />
        <limit lower="0" upper="1.56" effort="15" velocity="1" />
    </joint>

    <link name="r_thumb_DP">
        <inertial>
            <origin xyz="0.00405 5.6349E-08 -0.0045539" rpy="0 0 0" />
            <mass value="0.000001481" />
            <inertia ixx="2.6617E-08" ixy="7.6553E-14" ixz="-1.0548E-09" iyy="2.3083E-08" iyz="-1.0349E-12" izz="2.5034E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/Link5-3.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_collision/Link5-3.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_DIP" type="revolute">
        <origin xyz="-0.025096 0 0.005" rpy="0 0 0" />
        <parent link="r_thumb_MP" />
        <child link="r_thumb_DP" />
        <axis xyz="0 -1 0" />
        <limit lower="-1.396" upper="0" effort="15" velocity="1" />
    </joint>
    
    <link name="r_gelsight_thumb">
        <inertial>
            <origin xyz="-0.0091744 0.0076845 0.0023156" rpy="0 0 0" />
            <mass value="0.0027338" />
            <inertia ixx="9.3393E-06" ixy="1.1728E-05" ixz="-1.3724E-05" iyy="4.0772E-05" iyz="4.5505E-06" izz="3.9402E-05" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_fingerb.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/gelsight_fingerb.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_TIP_gelsight" type="fixed">
        <origin xyz="0 -0.001765 0.00175" rpy="-3.1416 0 -3.1416" />
        <parent link="r_thumb_DP" />
        <child link="r_gelsight_thumb" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_camera_link_thumb"/>
      
    <joint name="r_thumb_TIP_camera" type="fixed">
        <origin xyz="0.0040417 0.001765 0.0081471" rpy="3.1416 0.95993 3.1416" />
        <parent link="r_gelsight_thumb" />
        <child link="r_camera_link_thumb" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_thumb">
        <inertial>
            <origin xyz="0.013844 0.0017558 0.0025026" rpy="0 0 0" />
            <mass value="0.0010581" />
            <inertia ixx="2.0891E-08" ixy="-1.1599E-10" ixz="-9.2146E-09" iyy="3.1251E-08" iyz="-5.1903E-11" izz="4.2039E-08" />
        </inertial>
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerb_kz.STL" />
            </geometry>
            <material name="">
                <color rgba="0.89804 0.91765 0.92941 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <mesh filename="package://robot_controller/description/hand_right/hand_visual/elastomerb.STL" />
            </geometry>
        </collision>
    </link>

    <joint name="r_thumb_TIP_elastomer" type="fixed">
        <origin xyz="0 0 0" rpy="0 0 0" />
        <parent link="r_gelsight_thumb" />
        <child link="r_elastomer_thumb" />
        <axis xyz="0 0 0" />
    </joint>

    <link name="r_elastomer_tip_thumb" />
      
    <joint name="r_thumb_TIP_elastomer_tip" type="fixed">
        <origin xyz="0.015506 0.0017822 -0.00037374" rpy="3.1416 0.36356 3.1416" />
        <parent link="r_gelsight_thumb" />
        <child link="r_elastomer_tip_thumb" />
        <axis xyz="0 0 0" />
    </joint>

</robot>