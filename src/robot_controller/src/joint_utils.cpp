#include "robot_controller/joint_utils.h"
#include <iostream>

namespace robot_controller {

std::set<std::string> getMimicJoints() {
    // 根据URDF文件中的mimic关节定义
    return {
        "l_joint4_mimic",
        "r_joint4_mimic"
    };
}

JointType analyzeJointType(const pinocchio::Model& model, const std::string& joint_name) {
    std::set<std::string> mimic_joints = getMimicJoints();
    return (mimic_joints.find(joint_name) != mimic_joints.end()) ? JointType::MIMIC : JointType::ACTIVE;
}

std::vector<pinocchio::JointIndex> getActiveJoints(const pinocchio::Model& model, const std::string& prefix) {
    std::vector<pinocchio::JointIndex> active_joints;
    
    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) {
        const auto& joint = model.joints[j];
        const auto& name = model.names[j];
        
        // 检查关节是否有指定前缀且有自由度
        if (joint.nv() > 0 && name.substr(0, prefix.length()) == prefix) {
            JointType type = analyzeJointType(model, name);
            if (type == JointType::ACTIVE) {
                active_joints.push_back(j);
            }
        }
    }
    
    return active_joints;
}

std::vector<pinocchio::JointIndex> getMimicJoints(const pinocchio::Model& model, const std::string& prefix) {
    std::vector<pinocchio::JointIndex> mimic_joints;
    
    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) {
        const auto& joint = model.joints[j];
        const auto& name = model.names[j];
        
        // 检查关节是否有指定前缀且有自由度
        if (joint.nv() > 0 && name.substr(0, prefix.length()) == prefix) {
            JointType type = analyzeJointType(model, name);
            if (type == JointType::MIMIC) {
                mimic_joints.push_back(j);
            }
        }
    }
    
    return mimic_joints;
}

std::vector<JointInfo> getJointInfo(const pinocchio::Model& model, const std::string& prefix) {
    std::vector<JointInfo> joint_info;
    
    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) {
        const auto& joint = model.joints[j];
        const auto& name = model.names[j];
        
        // 检查关节是否有指定前缀且有自由度
        if (joint.nv() > 0 && name.substr(0, prefix.length()) == prefix) {
            JointType type = analyzeJointType(model, name);
            joint_info.emplace_back(j, name, type, joint.nq(), joint.nv());
        }
    }
    
    return joint_info;
}

int getActiveDOF(const pinocchio::Model& model, const std::string& prefix) {
    std::vector<pinocchio::JointIndex> active_joints = getActiveJoints(model, prefix);
    int total_dof = 0;
    
    for (const auto& j : active_joints) {
        const auto& joint = model.joints[j];
        total_dof += joint.nv();
    }
    
    return total_dof;
}

} // namespace robot_controller
