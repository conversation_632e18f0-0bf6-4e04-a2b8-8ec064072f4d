#include "robot_controller/def_class.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "test_pinocchio");
    ros::NodeHandle nh;

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model);

    pinocchio::Data data(model);

    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;
    std::cout << "\n=== 分析所有关节 ===" << std::endl;

    std::vector<pinocchio::JointIndex> right_arm_joints;
    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) // 0 是 root
    {
        const auto &joint = model.joints[j];
        const auto &name = model.names[j];
        std::cout << "Joint " << j << ": " << name << " (nq: " << joint.nq() << ", nv: " << joint.nv() << ")" << std::endl;

        // 检查是否为右臂关节（以"r_joint"开头）且为主动关节（nv > 0）
        if(joint.nv() > 0 && name.substr(0, 7) == "r_joint")
            right_arm_joints.push_back(j);
    }

    std::cout << "\n=== 右臂主动关节分析 ===" << std::endl;
    std::cout << "右臂主动关节数量: " << right_arm_joints.size() << std::endl;
    std::cout << "右臂主动关节列表: ";
    for(auto j: right_arm_joints)
        std::cout << model.names[j] << " ";
    std::cout << std::endl;

    // 计算右臂自由度
    int right_arm_dof = 0;
    for(const auto &j: right_arm_joints)
    {
        const auto &joint = model.joints[j];
        right_arm_dof += joint.nv();   // 每个关节的自由度
    }
    std::cout << "右臂总自由度: " << right_arm_dof << std::endl;

    // 验证：尝试获取右臂末端位姿
    std::cout << "\n=== 右臂末端位姿 ===" << std::endl;
    Eigen::VectorXd q = Eigen::VectorXd::Zero(model.nq);
    pinocchio::forwardKinematics(model, data, q);

    // 查找右臂末端关节
    try {
        pinocchio::JointIndex end_joint_id = model.getJointId("r_joint7");
        const auto& oMf = data.oMf[end_joint_id];
        std::cout << "右臂末端位置: " << oMf.translation().transpose() << std::endl;
    } catch (const std::exception& e) {
        std::cout << "无法找到右臂末端关节: " << e.what() << std::endl;
        // 尝试其他可能的末端关节名称
        for(const auto& name : {"r_Link7", "r_link7", "right_end_effector"}) {
            try {
                pinocchio::JointIndex end_joint_id = model.getJointId(name);
                const auto& oMf = data.oMf[end_joint_id];
                std::cout << "找到右臂末端关节 " << name << ", 位置: " << oMf.translation().transpose() << std::endl;
                break;
            } catch (...) {
                continue;
            }
        }
    }
    return 0;
}
