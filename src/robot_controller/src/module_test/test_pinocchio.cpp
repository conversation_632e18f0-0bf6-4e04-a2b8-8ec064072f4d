#include "robot_controller/def_class.h"
#include <set>

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "test_pinocchio");
    ros::NodeHandle nh;

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model);

    pinocchio::Data data(model);

    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;
    std::cout << "\n=== 分析所有关节 ===" << std::endl;

    // 定义从动关节（mimic关节）列表
    std::set<std::string> mimic_joints = {
        "l_joint4_mimic", "r_joint4_mimic"
    };

    std::vector<pinocchio::JointIndex> right_arm_joints;
    std::vector<pinocchio::JointIndex> right_arm_active_joints;
    std::vector<pinocchio::JointIndex> right_arm_mimic_joints;

    for (pinocchio::JointIndex j = 1; j < model.njoints; ++j) // 0 是 root
    {
        const auto &joint = model.joints[j];
        const auto &name = model.names[j];
        bool is_mimic = mimic_joints.find(name) != mimic_joints.end();

        std::cout << "Joint " << j << ": " << name
                  << " (nq: " << joint.nq() << ", nv: " << joint.nv()
                  << ", type: " << (is_mimic ? "MIMIC" : "ACTIVE") << ")" << std::endl;

        // 检查是否为右臂关节（以"r_joint"开头）
        if(joint.nv() > 0 && name.substr(0, 7) == "r_joint") {
            right_arm_joints.push_back(j);
            if (is_mimic) {
                right_arm_mimic_joints.push_back(j);
            } else {
                right_arm_active_joints.push_back(j);
            }
        }
    }

    std::cout << "\n=== 右臂关节分析 ===" << std::endl;
    std::cout << "右臂总关节数量: " << right_arm_joints.size() << std::endl;
    std::cout << "右臂主动关节数量: " << right_arm_active_joints.size() << std::endl;
    std::cout << "右臂从动关节数量: " << right_arm_mimic_joints.size() << std::endl;

    std::cout << "\n右臂主动关节列表: ";
    for(auto j: right_arm_active_joints)
        std::cout << model.names[j] << " ";
    std::cout << std::endl;

    std::cout << "右臂从动关节列表: ";
    for(auto j: right_arm_mimic_joints)
        std::cout << model.names[j] << " ";
    std::cout << std::endl;

    // 计算右臂自由度
    int right_arm_total_dof = 0;
    int right_arm_active_dof = 0;
    int right_arm_mimic_dof = 0;

    for(const auto &j: right_arm_joints) {
        const auto &joint = model.joints[j];
        right_arm_total_dof += joint.nv();
    }

    for(const auto &j: right_arm_active_joints) {
        const auto &joint = model.joints[j];
        right_arm_active_dof += joint.nv();
    }

    for(const auto &j: right_arm_mimic_joints) {
        const auto &joint = model.joints[j];
        right_arm_mimic_dof += joint.nv();
    }

    std::cout << "\n右臂总自由度: " << right_arm_total_dof << std::endl;
    std::cout << "右臂主动自由度: " << right_arm_active_dof << std::endl;
    std::cout << "右臂从动自由度: " << right_arm_mimic_dof << std::endl;

    // 验证：尝试获取右臂末端位姿
    std::cout << "\n=== 右臂末端位姿 ===" << std::endl;
    Eigen::VectorXd q = Eigen::VectorXd::Zero(model.nq);
    pinocchio::forwardKinematics(model, data, q);

    // 查找右臂末端关节
    try {
        pinocchio::JointIndex end_joint_id = model.getJointId("r_joint7");
        const auto& oMf = data.oMf[end_joint_id];
        std::cout << "右臂末端位置: " << oMf.translation().transpose() << std::endl;
    } catch (const std::exception& e) {
        std::cout << "无法找到右臂末端关节: " << e.what() << std::endl;
        // 尝试其他可能的末端关节名称
        for(const auto& name : {"r_Link7", "r_link7", "right_end_effector"}) {
            try {
                pinocchio::JointIndex end_joint_id = model.getJointId(name);
                const auto& oMf = data.oMf[end_joint_id];
                std::cout << "找到右臂末端关节 " << name << ", 位置: " << oMf.translation().transpose() << std::endl;
                break;
            } catch (...) {
                continue;
            }
        }
    }

    // 总结：如何正确区分主动和从动关节
    std::cout << "\n=== 总结 ===" << std::endl;
    std::cout << "1. 从动关节（mimic joints）在Pinocchio中仍然有nv=1，但它们的运动由主动关节驱动" << std::endl;
    std::cout << "2. 要正确区分主动和从动关节，需要解析URDF中的mimic标签" << std::endl;
    std::cout << "3. 右臂实际控制自由度：7个主动关节（r_joint1-7）" << std::endl;
    std::cout << "4. 右臂从动关节：1个（r_joint4_mimic，由r_joint4驱动）" << std::endl;

    return 0;
}
