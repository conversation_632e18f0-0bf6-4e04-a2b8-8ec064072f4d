#include "robot_controller/def_class.h"
#include "robot_controller/joint_utils.h"

int main(int argc, char *argv[])
{
    ros::init(argc, argv, "test_joint_utils");
    ros::NodeHandle nh;

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model);

    std::cout << "=== 使用工具函数分析关节 ===" << std::endl;
    
    // 分析右臂关节
    std::cout << "\n--- 右臂关节分析 ---" << std::endl;
    auto right_arm_info = robot_controller::getJointInfo(model, "r_joint");
    auto right_arm_active = robot_controller::getActiveJoints(model, "r_joint");
    auto right_arm_mimic = robot_controller::getMimicJoints(model, "r_joint");
    int right_arm_active_dof = robot_controller::getActiveDOF(model, "r_joint");
    
    std::cout << "右臂总关节数: " << right_arm_info.size() << std::endl;
    std::cout << "右臂主动关节数: " << right_arm_active.size() << std::endl;
    std::cout << "右臂从动关节数: " << right_arm_mimic.size() << std::endl;
    std::cout << "右臂主动自由度: " << right_arm_active_dof << std::endl;
    
    std::cout << "\n右臂关节详细信息:" << std::endl;
    for (const auto& info : right_arm_info) {
        std::cout << "  " << info.name 
                  << " (索引: " << info.index 
                  << ", 类型: " << (info.type == robot_controller::JointType::ACTIVE ? "主动" : "从动")
                  << ", nq: " << info.nq 
                  << ", nv: " << info.nv << ")" << std::endl;
    }
    
    // 分析左臂关节
    std::cout << "\n--- 左臂关节分析 ---" << std::endl;
    auto left_arm_info = robot_controller::getJointInfo(model, "l_joint");
    auto left_arm_active = robot_controller::getActiveJoints(model, "l_joint");
    auto left_arm_mimic = robot_controller::getMimicJoints(model, "l_joint");
    int left_arm_active_dof = robot_controller::getActiveDOF(model, "l_joint");
    
    std::cout << "左臂总关节数: " << left_arm_info.size() << std::endl;
    std::cout << "左臂主动关节数: " << left_arm_active.size() << std::endl;
    std::cout << "左臂从动关节数: " << left_arm_mimic.size() << std::endl;
    std::cout << "左臂主动自由度: " << left_arm_active_dof << std::endl;
    
    std::cout << "\n左臂关节详细信息:" << std::endl;
    for (const auto& info : left_arm_info) {
        std::cout << "  " << info.name 
                  << " (索引: " << info.index 
                  << ", 类型: " << (info.type == robot_controller::JointType::ACTIVE ? "主动" : "从动")
                  << ", nq: " << info.nq 
                  << ", nv: " << info.nv << ")" << std::endl;
    }
    
    // 分析基座关节
    std::cout << "\n--- 基座关节分析 ---" << std::endl;
    auto base_info = robot_controller::getJointInfo(model, "base_joint");
    int base_active_dof = robot_controller::getActiveDOF(model, "base_joint");
    
    std::cout << "基座关节数: " << base_info.size() << std::endl;
    std::cout << "基座主动自由度: " << base_active_dof << std::endl;
    
    std::cout << "\n基座关节详细信息:" << std::endl;
    for (const auto& info : base_info) {
        std::cout << "  " << info.name 
                  << " (索引: " << info.index 
                  << ", 类型: " << (info.type == robot_controller::JointType::ACTIVE ? "主动" : "从动")
                  << ", nq: " << info.nq 
                  << ", nv: " << info.nv << ")" << std::endl;
    }
    
    std::cout << "\n=== 总结 ===" << std::endl;
    std::cout << "现在可以正确区分主动和从动关节了！" << std::endl;
    std::cout << "主动关节：可以直接控制的关节" << std::endl;
    std::cout << "从动关节：运动由其他关节驱动的mimic关节" << std::endl;
    
    return 0;
}
